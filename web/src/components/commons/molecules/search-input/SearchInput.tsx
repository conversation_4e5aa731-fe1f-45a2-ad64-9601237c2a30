import { FormEventHandler } from 'react';
import * as React from 'react';
import PropTypes from 'prop-types';
import {
  Chat, ChatItem, ChatMessage, Input, InputProps, SearchIcon,
} from '@fluentui/react-northstar';
import { ValueOf } from '../../../../utilities/type';
import { SearchListMode, SearchModeType } from '../../../domains/split-view/types/SearchListMode';

// CSS
import './SearchInput.scss';
import { mergedClassName } from '../../../../utilities/commonFunction';

export interface ISearchInputProps {
  className?: string | null;
  value?: string | null;
  disabled?: boolean | null;
  onChange?: (value: string, e: React.SyntheticEvent<HTMLElement>) => void;
  onBlur?: (e: React.SyntheticEvent<HTMLElement>) => void;
  onSubmit?: (submittedValue: string, e: React.FormEvent) => void;
  searchMode?: SearchModeType;
  reply?: string | null;
}

const SearchInputView = {
  NOT_FOCUS_NO_VALUE: 'not-focus-no-value',
  NOT_FOCUS_WITH_VALUE: 'not-focus-with-value',
  FOCUS: 'focus',
};
type SearchInputViewType = ValueOf<typeof SearchInputView>;
// メッセージの型を定義
type Message = {
  sender: 'User' | 'atTane';
  text: string;
};

export const PlaceholderLabel = {
  BLANK: 'キーワードを入力',
  FOCUS: 'タイトル、分類、本文を検索',
};
export const PlaceholderLabelForAI = {
  BLANK: '自然言語で検索できます',
  FOCUS: '',
};

const SearchInput: React.FC<ISearchInputProps> = (props) => {
  const {
    className,
    value,
    disabled,
    onBlur,
    onChange,
    onSubmit,
    searchMode,
    reply,
  } = props;

  // このコンポーネントのviewは内部の状態管理にだけ使う
  const [view, setView] = React.useState<SearchInputViewType>(SearchInputView.NOT_FOCUS_NO_VALUE);

  // クラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('search-input', className);
    return mergedClassName(base, `is-${view}`);
    // return mergedClassName(viewAttached, isGuidSearch ? 'is-guid-search' : '');
  }, [className, view]);

  // プレースホルダテキスト
  // 非フォーカスで値が無いときだけ値を変える
  const placeholder = React.useMemo(() => {
    if (searchMode === SearchListMode.DEFAULT) {
      return view === SearchInputView.NOT_FOCUS_NO_VALUE
        ? PlaceholderLabel.BLANK
        : PlaceholderLabel.FOCUS;
    }

    return view === SearchInputView.NOT_FOCUS_NO_VALUE
      ? PlaceholderLabelForAI.BLANK
      : PlaceholderLabelForAI.FOCUS;
  }, [view, searchMode]);

  // 検索アイコン 非フォーカス時にだけ表示する
  const icon = React.useMemo(() => (
    view === SearchInputView.FOCUS ? undefined : <SearchIcon className="search-input-find-icon" />
  ), [view]);

  // 値の有無によってviewを設定する
  const setFocusView = React.useCallback(() => {
    if (value) {
      setView(SearchInputView.NOT_FOCUS_WITH_VALUE);
    } else {
      setView(SearchInputView.NOT_FOCUS_NO_VALUE);
    }
  }, [value]);

  // テキスト変更
  const handleOnChange: InputProps['onChange'] = React.useCallback((e, data?) => {
    // クリアボタンクリックのときはonClearを発火
    if (e.type === 'click' && onChange) {
      onChange(data?.value ?? '', e);
      return;
    }
    // それ以外のときはonChangeを発火
    if (onChange) onChange(data?.value ?? '', e);
  }, [onChange]);

  // フォーカス時に表示モードを変更してスクロールをロック
  const handleOnFocus: InputProps['onFocus'] = React.useCallback(() => {
    // if (isSP) setLocked(true);
    setView(SearchInputView.FOCUS);
  }, []);

  // フォーカス解除時に表示モードを変更してスクロールロックを解除
  const handleOnBlur: InputProps['onBlur'] = React.useCallback((e) => {
    // setLocked(false);
    setFocusView();
    if (onBlur) onBlur(e);
  }, [setFocusView, onBlur]);

  // 値が変更されたとき、フォーカス済でないときは表示モードを変更
  React.useEffect(() => {
    if (view === SearchInputView.FOCUS) return;
    setFocusView();
  }, [view, setFocusView]);

  const [messages, setMessages] = React.useState<Message[]>(
    [{ sender: 'atTane', text: 'お手伝いできることはありますか？' }],
  );

  // メッセージ送信の処理
  const displayMessage = React.useCallback(() => {
    setMessages((prevMessages) => [
      ...prevMessages,
      { sender: 'User', text: value ?? '' }, // valueがチャット欄に残る
    ]);
  }, [value]);

  React.useEffect(() => {
    if (reply) {
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          sender: 'atTane',
          text: '検索が完了しました',
        },
      ]);
    }
  }, [reply]);

  // enter押下でsubmitを発火
  const handleOnSubmit: FormEventHandler = React.useCallback((e) => {
    e.preventDefault();
    // 空欄では検索できない
    if (!value || value.trim() === '') return;
    if (onSubmit) onSubmit(value, e);
    // Chat モードの場合のみメッセージ送信処理を実行
    // これをオフにするとデフォルトの検索文言がチャットに表示される
    if (searchMode === SearchListMode.Chat) {
      // こいつが検索文言を表示している
      // bookmarkModeの時は発火しない
      // エンター押したらすでにchat欄内に表示されるはず
      displayMessage();
    }
  }, [displayMessage, onSubmit, value, searchMode]);

  // 最新のメッセージを追跡するための参照
  const latestMessageRef = React.useRef<HTMLDivElement | null>(null);
  // メッセージが更新されるたびに最新のメッセージにスクロール
  React.useEffect(() => {
    if (latestMessageRef.current) {
      latestMessageRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  return (
  // formで囲みactionを指定しないとiOSのキーボードが「検索」表記にならない
  // https://stackoverflow.com/questions/4864167/show-search-button-in-iphone-ipad-safari-keyboard
    <form className={rootClassName} action="." onSubmit={handleOnSubmit}>
      {searchMode === SearchListMode.DEFAULT ? (
        <div>
          <Input
            icon={icon}
            iconPosition={value ? 'end' : 'start'}
            placeholder={placeholder}
            value={value ?? ''}
            onFocus={handleOnFocus}
            onBlur={handleOnBlur}
            onChange={handleOnChange}
            disabled={disabled ?? false}
            type="search"
            inverted
            fluid
            clearable
          />
          {/* <InformationButton onClick={handleOnChange} /> */}
        </div>
      ) : (
        <div>
          <Chat className="chat-container">
            {/* messagesの数だけ回る */}
            {/* 表示されるのでchatModeであることは認識している */}
            <ChatItem attached>
              {messages.map((msg) => (
                msg.sender === 'User' ? (
                  <div className="ChatMyMessage">
                    <ChatMessage mine>{msg.text}</ChatMessage>
                  </div>
                ) : (
                  <div className="ChatMessage">
                    <ChatMessage>{msg.text}</ChatMessage>
                  </div>
                )
              ))}
            </ChatItem>
            <div ref={latestMessageRef} />
          </Chat>
          <Input
            icon={icon}
            iconPosition={value ? 'end' : 'start'}
            placeholder={placeholder}
            value={value ?? ''}
            onFocus={handleOnFocus}
            onBlur={handleOnBlur}
            onChange={handleOnChange}
            disabled={disabled ?? false}
            type="search"
            inverted
            fluid
            clearable
          />
        </div>
      )}
    </form>
  );
};

SearchInput.propTypes = {
  className: PropTypes.string,
  value: PropTypes.string,
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onSubmit: PropTypes.func,
  searchMode: PropTypes.string,
  reply: PropTypes.string,
};

SearchInput.defaultProps = {
  className: undefined,
  value: null,
  disabled: null,
  onChange: undefined,
  onBlur: undefined,
  onSubmit: undefined,
  searchMode: SearchListMode.DEFAULT,
  reply: null,
};

export default SearchInput;
